import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/services/reminder_settings_watcher.dart';
import '../../domain/services/foreground_resume_handler.dart';
import '../../domain/models/reminder_state.dart';
import '../../domain/models/reminder_error.dart';
import '../providers/reminder_provider.dart';
import '../../../alerts/presentation/providers/alert_provider.dart';
import '../../../validation/presentation/providers/validation_alert_provider.dart';

/// Widget that listens to settings changes and triggers reactive notification updates
class ReactiveNotificationListener extends ConsumerStatefulWidget {
  final Widget child;

  const ReactiveNotificationListener({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<ReactiveNotificationListener> createState() =>
      _ReactiveNotificationListenerState();
}

class _ReactiveNotificationListenerState
    extends ConsumerState<ReactiveNotificationListener>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize foreground resume handler
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ForegroundResumeHandler.initialize(ref);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Handle app lifecycle changes for background flag processing
    ForegroundResumeHandler.handleAppLifecycleStateChange(
      ref,
      state.toString().split('.').last,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Listen to settings changes and trigger reminder updates
    ref.listen<AsyncValue<SettingsState>>(
      settingsProvider,
      (previous, next) {
        _handleSettingsChange(previous, next);
      },
    );

    // Listen to reminder state changes for user feedback
    ref.listen<AsyncValue<ReminderState>>(
      reminderProvider,
      (previous, next) {
        _handleReminderStateChange(previous, next);
      },
    );

    // Listen to alert state changes for user feedback
    ref.listen(
      alertProvider,
      (previous, next) {
        _handleAlertStateChange(previous, next);
      },
    );

    // Listen to validation alert state changes for user feedback
    ref.listen(
      validationAlertProvider,
      (previous, next) {
        _handleValidationAlertStateChange(previous, next);
      },
    );

    return widget.child;
  }

  /// Handle settings changes and trigger reminder updates
  void _handleSettingsChange(
    AsyncValue<SettingsState>? previous,
    AsyncValue<SettingsState> next,
  ) {
    next.whenData((currentSettings) {
      previous?.whenData((previousSettings) {
        // Check for reminder-specific changes
        if (ReminderSettingsWatcher.hasReminderChanges(
            previousSettings, currentSettings)) {
          final changes = ReminderSettingsWatcher.detectReminderChanges(
            previousSettings,
            currentSettings,
          );

          Logger.info(
              'ReactiveReminderListener: Detected reminder settings changes: ${ReminderSettingsWatcher.getChangesDescription(changes)}');

          // Validate settings before updating
          final validationErrors =
              ReminderSettingsWatcher.validateReminderSettings(currentSettings);
          if (validationErrors.isNotEmpty) {
            Logger.error(
                'ReactiveReminderListener: Invalid reminder settings: ${validationErrors.join(', ')}');
            _showValidationError(validationErrors.first);
            return;
          }

          // Update reminder provider with new settings
          _updateReminderSettings(changes, currentSettings);
        }

        // Check for alert-related settings changes
        _handleAlertSettingsChanges(previousSettings, currentSettings);

        // Check for validation alert settings changes
        _handleValidationAlertSettingsChanges(
            previousSettings, currentSettings);
      });

      // Handle initial load case
      if (previous == null) {
        Logger.info(
            'ReactiveNotificationListener: Initial settings load, checking notification configuration');
        _handleInitialSettingsLoad(currentSettings);
      }
    });
  }

  /// Handle reminder state changes for user feedback
  void _handleReminderStateChange(
    AsyncValue<ReminderState>? previous,
    AsyncValue<ReminderState> next,
  ) {
    next.whenData((currentState) {
      previous?.whenData((previousState) {
        // Show feedback for state changes
        if (previousState.status != currentState.status) {
          _showReminderStatusFeedback(currentState);
        }

        // Handle errors
        if (currentState.hasError && !previousState.hasError) {
          _showReminderError(currentState.error!);
        }
      });
    });
  }

  /// Update reminder settings in the provider
  void _updateReminderSettings(
    Map<String, dynamic> changes,
    SettingsState currentSettings,
  ) {
    try {
      final reminderNotifier = ref.read(reminderProvider.notifier);

      reminderNotifier.updateSettings(
        enabled:
            changes.containsKey('enabled') ? changes['enabled'] as bool : null,
        frequency: changes.containsKey('frequency')
            ? changes['frequency'] as String
            : null,
        startDateTime: changes.containsKey('startDateTime')
            ? (changes['startDateTime'] != null
                ? DateTime.parse(changes['startDateTime'] as String)
                : null)
            : null,
      );
    } catch (e) {
      Logger.error(
          'ReactiveReminderListener: Error updating reminder settings: $e');
      _showError('Failed to update reminder settings');
    }
  }

  /// Handle initial settings load
  void _handleInitialSettingsLoad(SettingsState settings) {
    if (settings.remindersEnabled) {
      final validationErrors =
          ReminderSettingsWatcher.validateReminderSettings(settings);
      if (validationErrors.isEmpty) {
        // Initialize reminder with current settings
        _updateReminderSettings({
          'enabled': settings.remindersEnabled,
          'frequency': settings.reminderFrequency,
          'startDateTime': settings.reminderStartDateTime?.toIso8601String(),
        }, settings);
      } else {
        Logger.warning(
            'ReactiveReminderListener: Invalid reminder settings on initial load: ${validationErrors.join(', ')}');
      }
    }
  }

  /// Show reminder status feedback to user
  void _showReminderStatusFeedback(ReminderState state) {
    String? message;

    switch (state.status) {
      case ReminderStatus.scheduled:
        if (state.nextReminderDate != null) {
          final timeUntil = state.nextReminderDate!.difference(DateTime.now());
          if (timeUntil.inDays > 0) {
            message = 'Reminder scheduled in ${timeUntil.inDays} days';
          } else if (timeUntil.inHours > 0) {
            message = 'Reminder scheduled in ${timeUntil.inHours} hours';
          } else {
            message = 'Reminder scheduled soon';
          }
        } else {
          message = 'Reminder scheduled successfully';
        }
        break;
      case ReminderStatus.cancelled:
        message = 'Reminder cancelled';
        break;
      case ReminderStatus.fired:
        message = 'Reminder fired - next one will be scheduled automatically';
        break;
      default:
        // Don't show feedback for other states
        break;
    }

    if (message != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Show reminder error to user
  void _showReminderError(ReminderError error) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reminder Error'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(error.userMessage),
            if (error.primaryRecoveryAction != null) ...[
              const SizedBox(height: 16),
              Text(
                'Suggested action: ${error.primaryRecoveryAction}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
        actions: [
          if (error.canRetry)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref.read(reminderProvider.notifier).retry();
              },
              child: const Text('Retry'),
            ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(reminderProvider.notifier).clearError();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show validation error to user
  void _showValidationError(String error) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Reminder settings error: $error'),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  /// Show generic error to user
  void _showError(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Handle alert settings changes
  void _handleAlertSettingsChanges(
    SettingsState previousSettings,
    SettingsState currentSettings,
  ) {
    try {
      bool hasChanges = false;
      final changes = <String, dynamic>{};

      // Check for notification enabled changes
      if (previousSettings.notificationsEnabled !=
          currentSettings.notificationsEnabled) {
        changes['enabled'] = currentSettings.notificationsEnabled;
        hasChanges = true;
      }

      // Check for low balance alert changes
      if (previousSettings.lowBalanceAlertsEnabled !=
          currentSettings.lowBalanceAlertsEnabled) {
        changes['lowBalanceEnabled'] = currentSettings.lowBalanceAlertsEnabled;
        hasChanges = true;
      }

      // Check for alert threshold changes
      if (previousSettings.timeToTopUpAlertsEnabled !=
          currentSettings.timeToTopUpAlertsEnabled) {
        changes['alertThresholdEnabled'] =
            currentSettings.timeToTopUpAlertsEnabled;
        hasChanges = true;
      }

      // Check for threshold value changes
      if (previousSettings.alertThreshold != currentSettings.alertThreshold) {
        changes['alertThreshold'] = currentSettings.alertThreshold;
        hasChanges = true;
      }

      // Check for days in advance changes
      if (previousSettings.daysInAdvance != currentSettings.daysInAdvance) {
        changes['daysInAdvance'] = currentSettings.daysInAdvance;
        hasChanges = true;
      }

      if (hasChanges) {
        Logger.info(
            'ReactiveNotificationListener: Alert settings changed: $changes');

        final alertNotifier = ref.read(alertProvider.notifier);
        alertNotifier.updateSettings(
          enabled: changes.containsKey('enabled')
              ? changes['enabled'] as bool
              : null,
          lowBalanceEnabled: changes.containsKey('lowBalanceEnabled')
              ? changes['lowBalanceEnabled'] as bool
              : null,
          alertThresholdEnabled: changes.containsKey('alertThresholdEnabled')
              ? changes['alertThresholdEnabled'] as bool
              : null,
          alertThreshold: changes.containsKey('alertThreshold')
              ? changes['alertThreshold'] as double
              : null,
          daysInAdvance: changes.containsKey('daysInAdvance')
              ? changes['daysInAdvance'] as int
              : null,
        );
      }
    } catch (e) {
      Logger.error(
          'ReactiveNotificationListener: Error handling alert settings changes: $e');
      _showError('Failed to update alert settings');
    }
  }

  /// Handle validation alert settings changes
  void _handleValidationAlertSettingsChanges(
    SettingsState previousSettings,
    SettingsState currentSettings,
  ) {
    try {
      // Check if validation alerts should be enabled/disabled
      final previousEnabled = previousSettings.notificationsEnabled ||
          previousSettings.invalidRecordAlertsEnabled;
      final currentEnabled = currentSettings.notificationsEnabled ||
          currentSettings.invalidRecordAlertsEnabled;

      if (previousEnabled != currentEnabled) {
        Logger.info(
            'ReactiveNotificationListener: Validation alert settings changed: enabled=$currentEnabled');

        final validationAlertNotifier =
            ref.read(validationAlertProvider.notifier);
        validationAlertNotifier.updateSettings(enabled: currentEnabled);
      }
    } catch (e) {
      Logger.error(
          'ReactiveNotificationListener: Error handling validation alert settings changes: $e');
      _showError('Failed to update validation alert settings');
    }
  }

  /// Handle alert state changes for user feedback
  void _handleAlertStateChange(
    AsyncValue<dynamic>? previous,
    AsyncValue<dynamic> next,
  ) {
    next.whenData((currentState) {
      if (currentState.hasError) {
        _showError(
            'Alert error: ${currentState.error?.userMessage ?? 'Unknown error'}');
      }
    });
  }

  /// Handle validation alert state changes for user feedback
  void _handleValidationAlertStateChange(
    AsyncValue<dynamic>? previous,
    AsyncValue<dynamic> next,
  ) {
    next.whenData((currentState) {
      if (currentState.hasError) {
        _showError(
            'Validation alert error: ${currentState.error?.userMessage ?? 'Unknown error'}');
      }
    });
  }
}
