import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../../features/notifications/domain/models/notification.dart';

/// Service for smart notification management with deduplication and batching
class SmartNotificationService {
  static const String _lastNotificationPrefix = 'last_notification_';
  static const Duration _deduplicationWindow = Duration(hours: 24);

  /// Check if a notification should be fired based on deduplication rules
  Future<bool> shouldFireNotification(AppNotification notification) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '${_lastNotificationPrefix}${notification.type.toString()}';
      final lastFiredTimestamp = prefs.getInt(key);

      if (lastFiredTimestamp == null) {
        return true; // First time firing this notification type
      }

      final lastFired = DateTime.fromMillisecondsSinceEpoch(lastFiredTimestamp);
      final now = DateTime.now();
      final timeSinceLastFired = now.difference(lastFired);

      // Don't fire if within deduplication window
      if (timeSinceLastFired < _deduplicationWindow) {
        Logger.info(
          'SmartNotificationService: Skipping ${notification.type} notification '
          '(last fired ${timeSinceLastFired.inHours} hours ago)',
        );
        return false;
      }

      return true;
    } catch (e) {
      Logger.error(
          'SmartNotificationService: Error checking deduplication: $e');
      return true; // Default to firing on error
    }
  }

  /// Record that a notification was fired for deduplication tracking
  Future<void> recordNotificationFired(AppNotification notification) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_lastNotificationPrefix${notification.type.toString()}';
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setInt(key, timestamp);

      Logger.info(
        'SmartNotificationService: Recorded ${notification.type} notification fired',
      );
    } catch (e) {
      Logger.error(
          'SmartNotificationService: Error recording notification: $e');
    }
  }

  /// Get the last fired timestamp for a notification type
  Future<DateTime?> getLastFiredTimestamp(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_lastNotificationPrefix${type.toString()}';
      final timestamp = prefs.getInt(key);

      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }

      return null;
    } catch (e) {
      Logger.error(
          'SmartNotificationService: Error getting last fired timestamp: $e');
      return null;
    }
  }

  /// Clear all notification data
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      // Remove all notification-related keys
      for (final key in keys) {
        if (key.startsWith(_lastNotificationPrefix)) {
          await prefs.remove(key);
        }
      }

      Logger.info('SmartNotificationService: Cleared all notification data');
    } catch (e) {
      Logger.error('SmartNotificationService: Error clearing data: $e');
    }
  }

  /// Get notification history summary
  Future<Map<NotificationType, DateTime?>> getNotificationHistory() async {
    final history = <NotificationType, DateTime?>{};

    for (final type in NotificationType.values) {
      history[type] = await getLastFiredTimestamp(type);
    }

    return history;
  }

  /// Check if any notifications have been fired recently
  Future<bool> hasRecentNotifications({Duration? within}) async {
    within ??= _deduplicationWindow;

    try {
      final history = await getNotificationHistory();
      final now = DateTime.now();

      for (final lastFired in history.values) {
        if (lastFired != null) {
          final timeSince = now.difference(lastFired);
          if (timeSince < within) {
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      Logger.error(
          'SmartNotificationService: Error checking recent notifications: $e');
      return false;
    }
  }

  /// Batch multiple notifications into a single summary notification
  AppNotification? batchNotifications(List<AppNotification> notifications) {
    if (notifications.isEmpty) return null;
    if (notifications.length == 1) return notifications.first;

    // Create summary notification
    final types = notifications.map((n) => n.type).toSet();
    const title = 'Multiple Alerts';
    final message = 'You have ${notifications.length} active alerts: '
        '${types.map((t) => _getTypeDisplayName(t)).join(', ')}';

    return AppNotification(
      title: title,
      message: message,
      timestamp: DateTime.now(),
      type: NotificationType.lowBalance, // Use first type as primary
    );
  }

  /// Get display name for notification type
  String _getTypeDisplayName(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return 'Low Balance';
      case NotificationType.timeToTopUp:
        return 'Time to Top-Up';
      case NotificationType.invalidRecord:
        return 'Invalid Record';
      case NotificationType.readingReminder:
        return 'Reminder';
      case NotificationType.welcome:
        return 'Welcome';
      case NotificationType.appUpdate:
        return 'Update';
    }
  }
}
