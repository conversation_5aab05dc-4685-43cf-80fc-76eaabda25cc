import 'package:workmanager/workmanager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';
import 'reminder_scheduling_service.dart';

/// Service for managing background monitoring of notification conditions
class BackgroundMonitoringService {
  static final BackgroundMonitoringService _instance =
      BackgroundMonitoringService._internal();

  factory BackgroundMonitoringService() => _instance;
  BackgroundMonitoringService._internal();

  static const String _taskName = 'lekky_notification_check';
  static const String _uniqueName = 'lekky_background_monitor';

  /// Initialize background monitoring
  Future<void> initialize() async {
    try {
      await Workmanager().initialize(
        _callbackDispatcher,
        isInDebugMode: false, // Set to true for debugging
      );
      Logger.info('Background monitoring service initialized');
    } catch (e) {
      Logger.error('Failed to initialize background monitoring: $e');
    }
  }

  /// Start background monitoring (every 6 hours)
  Future<void> startMonitoring() async {
    try {
      // Cancel any existing tasks first
      await stopMonitoring();

      // Register periodic task (every 6 hours)
      await Workmanager().registerPeriodicTask(
        _uniqueName,
        _taskName,
        frequency: const Duration(hours: 6),
        constraints: Constraints(
          networkType: NetworkType.not_required,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
      );

      Logger.info('Background monitoring started (6-hour intervals)');
    } catch (e) {
      Logger.error('Failed to start background monitoring: $e');
    }
  }

  /// Stop background monitoring
  Future<void> stopMonitoring() async {
    try {
      await Workmanager().cancelByUniqueName(_uniqueName);
      Logger.info('Background monitoring stopped');
    } catch (e) {
      Logger.error('Failed to stop background monitoring: $e');
    }
  }

  /// Check if monitoring should be active based on notification settings
  Future<bool> shouldMonitoringBeActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final notificationsEnabled =
          prefs.getBool(PreferenceKeys.notificationsEnabled) ?? false;
      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      final remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      return (notificationsEnabled &&
              (lowBalanceEnabled ||
                  timeToTopUpEnabled ||
                  invalidRecordEnabled)) ||
          remindersEnabled;
    } catch (e) {
      Logger.error('Error checking monitoring status: $e');
      return false;
    }
  }

  /// Update monitoring based on current settings
  Future<void> updateMonitoring() async {
    try {
      final shouldMonitor = await shouldMonitoringBeActive();

      if (shouldMonitor) {
        await startMonitoring();
      } else {
        await stopMonitoring();
      }
    } catch (e) {
      Logger.error('Error updating monitoring: $e');
    }
  }
}

/// Background task callback dispatcher
@pragma('vm:entry-point')
void _callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      Logger.info('Background notification check started');

      // Load notification settings
      final prefs = await SharedPreferences.getInstance();
      final notificationsEnabled =
          prefs.getBool(PreferenceKeys.notificationsEnabled) ?? false;
      final remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      if (!notificationsEnabled && !remindersEnabled) {
        Logger.info('Notifications and reminders disabled, skipping check');
        return Future.value(true);
      }

      // Background task handles both reminders and critical alerts
      Logger.info(
          'Background task running - checking reminders and critical alerts');

      // Check and update reminders if enabled
      if (remindersEnabled) {
        try {
          final reminderService = ReminderSchedulingService();
          await reminderService.updateReminders();
          Logger.info('Reminder maintenance completed');
        } catch (e) {
          Logger.error('Reminder maintenance failed: $e');
        }
      }

      // Check critical alerts if notifications enabled
      if (notificationsEnabled) {
        try {
          await _checkCriticalAlertsInBackground();
          Logger.info('Background alert check completed');
        } catch (e) {
          Logger.error('Background alert check failed: $e');
        }
      }

      return Future.value(true);
    } catch (e) {
      Logger.error('Background task failed: $e');
      return Future.value(false);
    }
  });
}

/// Check critical alerts in background context
Future<void> _checkCriticalAlertsInBackground() async {
  try {
    // Import required for background context
    // Note: This runs in isolate, so we need to recreate services
    final prefs = await SharedPreferences.getInstance();

    // Load alert settings
    final notificationsEnabled =
        prefs.getBool(PreferenceKeys.notificationsEnabled) ?? false;
    final lowBalanceEnabled =
        prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
    final timeToTopUpEnabled =
        prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;

    // Check if any notification type is enabled (master switch OR individual types)
    final anyNotificationEnabled =
        notificationsEnabled || lowBalanceEnabled || timeToTopUpEnabled;

    if (!anyNotificationEnabled) {
      Logger.info('Background alerts: No alerts enabled, skipping');
      return;
    }

    // For background context, we'll use a simplified approach
    // Store a flag that foreground should check alerts immediately
    await prefs.setBool('background_alert_check_requested', true);
    await prefs.setString(
        'background_alert_check_time', DateTime.now().toIso8601String());

    Logger.info('Background alerts: Requested foreground alert check');
  } catch (e) {
    Logger.error('Background alerts: Error checking critical alerts: $e');
  }
}
