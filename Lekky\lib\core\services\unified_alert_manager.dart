import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';
import '../di/service_locator.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/data/notification_service.dart';
import '../../features/home/<USER>/models/dashboard_state.dart';
import '../../features/averages/domain/services/average_service.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../features/top_ups/domain/repositories/top_up_repository.dart';
import '../utils/average_calculator.dart';

/// Unified service for managing all alert types with reactive triggers
class UnifiedAlertManager {
  static final UnifiedAlertManager _instance = UnifiedAlertManager._internal();
  factory UnifiedAlertManager() => _instance;
  UnifiedAlertManager._internal();

  /// Check all alert conditions and fire notifications immediately if met
  Future<void> checkAndFireAlerts() async {
    try {
      Logger.info('UnifiedAlertManager: Starting alert check');

      // Load alert settings
      final settings = await _loadAlertSettings();

      // Check if any notification type is enabled (individual types only)
      final anyNotificationEnabled = settings['lowBalanceEnabled'] ||
          settings['timeToTopUpEnabled'] ||
          settings['invalidRecordEnabled'];

      if (!anyNotificationEnabled) {
        Logger.info(
            'UnifiedAlertManager: All notifications disabled, skipping alerts');
        return;
      }

      // Get dashboard state for calculations
      final dashboardState = await _getDashboardState();
      if (dashboardState == null) {
        Logger.warning('UnifiedAlertManager: No dashboard state available');
        return;
      }

      final notifications = <AppNotification>[];

      // Check low balance condition (24hrs before meter zero)
      if (settings['lowBalanceEnabled']) {
        final lowBalanceAlert = await _checkLowBalanceCondition(dashboardState);
        if (lowBalanceAlert != null) {
          notifications.add(lowBalanceAlert);
        }
      }

      // Check alert threshold condition
      if (settings['timeToTopUpEnabled']) {
        final thresholdAlert = await _checkThresholdCondition(
          dashboardState,
          settings['alertThreshold'],
          settings['daysInAdvance'],
        );
        if (thresholdAlert != null) {
          notifications.add(thresholdAlert);
        }
      }

      // Fire all notifications
      await _fireNotifications(notifications);

      Logger.info(
          'UnifiedAlertManager: Alert check completed, fired ${notifications.length} notifications');
    } catch (e) {
      Logger.error('UnifiedAlertManager: Error in alert check: $e');
      await _attemptFallbackNotification(e);
    }
  }

  /// Check low balance condition (within 24 hours of meter zero)
  Future<AppNotification?> _checkLowBalanceCondition(
      DashboardState dashboardState) async {
    try {
      final daysToZero = AverageCalculator.calculateDaysToMeterZero(
        lastMeterReading: dashboardState.latestMeterReading!.value,
        topUpsSinceLastReading: dashboardState.totalTopUpsAfterLatestReading,
        lastReadingDate: dashboardState.latestMeterReading!.date,
        recentAverageUsage: dashboardState.recentAverageDailyUsage,
        totalAverageUsage: dashboardState.totalAverageDailyUsage,
      );

      // Check if condition is met (< 24 hours to zero but not already zero)
      if (daysToZero == null || daysToZero >= 1.0 || daysToZero <= 0) {
        return null;
      }

      // Check deduplication
      if (await _wasNotificationSentToday(NotificationType.lowBalance)) {
        return null;
      }

      // Create and track notification
      await _setLastNotificationDate(NotificationType.lowBalance);

      final hoursRemaining = (daysToZero * 24).round();
      return AppNotification(
        title: 'Low Balance Alert',
        message: hoursRemaining > 0
            ? 'Your meter will reach zero in approximately $hoursRemaining hours.'
            : 'Your meter balance is critically low.',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );
    } catch (e) {
      Logger.error(
          'UnifiedAlertManager: Error checking low balance condition: $e');
      return null;
    }
  }

  /// Check alert threshold condition
  Future<AppNotification?> _checkThresholdCondition(
    DashboardState dashboardState,
    double alertThreshold,
    int daysInAdvance,
  ) async {
    try {
      final daysToThreshold = AverageCalculator.calculateDaysToAlertThreshold(
        lastMeterReading: dashboardState.latestMeterReading!.value,
        topUpsSinceLastReading: dashboardState.totalTopUpsAfterLatestReading,
        lastReadingDate: dashboardState.latestMeterReading!.date,
        alertThreshold: alertThreshold,
        recentAverageUsage: dashboardState.recentAverageDailyUsage,
        totalAverageUsage: dashboardState.totalAverageDailyUsage,
        daysInAdvance: daysInAdvance,
      );

      // Check if condition is met (< 24 hours to threshold)
      if (daysToThreshold == null || daysToThreshold >= 1.0) {
        return null;
      }

      // Check deduplication
      if (await _wasNotificationSentToday(NotificationType.timeToTopUp)) {
        return null;
      }

      // Create and track notification
      await _setLastNotificationDate(NotificationType.timeToTopUp);

      final hoursRemaining = (daysToThreshold * 24).round();
      return AppNotification(
        title: 'Time to Top-Up',
        message: hoursRemaining > 0
            ? 'You should top up in approximately $hoursRemaining hours.'
            : 'It\'s time to top up your meter.',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );
    } catch (e) {
      Logger.error(
          'UnifiedAlertManager: Error checking threshold condition: $e');
      return null;
    }
  }

  /// Fire notifications with retry logic
  Future<void> _fireNotifications(List<AppNotification> notifications) async {
    if (notifications.isEmpty) return;

    final notificationService =
        await serviceLocator.getAsync<NotificationService>();

    for (final notification in notifications) {
      await _fireNotificationWithRetry(notificationService, notification);
    }
  }

  /// Fire single notification with retry and fallback
  Future<void> _fireNotificationWithRetry(
    NotificationService notificationService,
    AppNotification notification,
  ) async {
    const maxRetries = 3;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await notificationService.showNotification(notification);
        Logger.info(
            'UnifiedAlertManager: Successfully fired ${notification.type} notification');
        return;
      } catch (e) {
        Logger.error(
            'UnifiedAlertManager: Attempt $attempt failed for ${notification.type}: $e');

        if (attempt == maxRetries) {
          // Final attempt failed - show in-app alert
          await _showInAppAlert(notification);
        } else {
          // Wait before retry
          await Future.delayed(Duration(milliseconds: 500 * attempt));
        }
      }
    }
  }

  /// Show in-app alert as fallback when notifications fail
  Future<void> _showInAppAlert(AppNotification notification) async {
    try {
      // Store alert for UI to pick up
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_in_app_alert_title', notification.title);
      await prefs.setString(
          'pending_in_app_alert_message', notification.message);
      await prefs.setString(
          'pending_in_app_alert_type', notification.type.toString());
      await prefs.setString(
          'pending_in_app_alert_timestamp', DateTime.now().toIso8601String());

      Logger.info(
          'UnifiedAlertManager: Stored in-app alert for ${notification.type}');
    } catch (e) {
      Logger.error('UnifiedAlertManager: Failed to store in-app alert: $e');
    }
  }

  /// Get dashboard state for calculations
  Future<DashboardState?> _getDashboardState() async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final topUpRepo = serviceLocator<TopUpRepository>();
      final averageService = serviceLocator<AverageService>();

      // Get latest meter reading
      final latestMeterReading = await meterReadingRepo.getLatestMeterReading();
      if (latestMeterReading == null) return null;

      // Get averages
      final averageResult = await averageService.getAverages();

      // Calculate top-ups after latest reading
      final topUps = await topUpRepo.getTopUpsByDateRange(
        startDate: latestMeterReading.date,
        endDate: DateTime.now(),
      );

      final topUpsAfterLatest = topUps
          .where((topUp) => topUp.date.isAfter(latestMeterReading.date))
          .fold<double>(0.0, (sum, topUp) => sum + topUp.amount);

      return DashboardState(
        latestMeterReading: latestMeterReading,
        recentAverageDailyUsage: averageResult.recentAverage,
        totalAverageDailyUsage: averageResult.totalAverage,
        totalTopUpsAfterLatestReading: topUpsAfterLatest,
        recentEntries: const [],
        isLoading: false,
      );
    } catch (e) {
      Logger.error('UnifiedAlertManager: Error getting dashboard state: $e');
      return null;
    }
  }

  /// Load alert settings from preferences
  Future<Map<String, dynamic>> _loadAlertSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'lowBalanceEnabled':
            prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false,
        'timeToTopUpEnabled':
            prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false,
        'invalidRecordEnabled':
            prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false,
        'alertThreshold': prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0,
        'daysInAdvance': prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5,
      };
    } catch (e) {
      Logger.error('UnifiedAlertManager: Error loading alert settings: $e');
      return {
        'lowBalanceEnabled': false,
        'timeToTopUpEnabled': false,
        'invalidRecordEnabled': false,
        'alertThreshold': 5.0,
        'daysInAdvance': 5,
      };
    }
  }

  /// Check if notification was sent today (deduplication)
  Future<bool> _wasNotificationSentToday(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      final lastDateString = prefs.getString(key);

      if (lastDateString == null) return false;

      final lastDate = DateTime.parse(lastDateString);
      final today = DateTime.now();

      return lastDate.year == today.year &&
          lastDate.month == today.month &&
          lastDate.day == today.day;
    } catch (e) {
      Logger.error('UnifiedAlertManager: Error checking notification date: $e');
      return false;
    }
  }

  /// Set last notification date for deduplication
  Future<void> _setLastNotificationDate(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      await prefs.setString(key, DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error('UnifiedAlertManager: Error setting notification date: $e');
    }
  }

  /// Get preference key for notification date tracking
  String _getNotificationDateKey(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return PreferenceKeys.lastLowBalanceNotificationDate;
      case NotificationType.timeToTopUp:
        return PreferenceKeys.lastTimeToTopUpNotificationDate;
      default:
        throw ArgumentError(
            'Unsupported notification type for deduplication: $type');
    }
  }

  /// Attempt fallback notification when system fails
  Future<void> _attemptFallbackNotification(dynamic error) async {
    try {
      final fallbackAlert = AppNotification(
        title: 'Alert System Error',
        message: 'Unable to check alerts. Please check your meter manually.',
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord,
      );

      await _showInAppAlert(fallbackAlert);
    } catch (e) {
      Logger.error(
          'UnifiedAlertManager: Even fallback notification failed: $e');
    }
  }
}
