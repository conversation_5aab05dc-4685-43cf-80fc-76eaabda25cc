import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/notification_debug_helper.dart';
import '../../../../core/services/unified_alert_manager.dart';
import '../../../../core/services/notification_permission_manager.dart';
import '../../domain/models/notification.dart';
import '../../data/notification_service.dart';
import '../../../../core/di/service_locator.dart';

/// Debug screen for testing notification functionality
class NotificationDebugScreen extends ConsumerStatefulWidget {
  const NotificationDebugScreen({super.key});

  @override
  ConsumerState<NotificationDebugScreen> createState() =>
      _NotificationDebugScreenState();
}

class _NotificationDebugScreenState
    extends ConsumerState<NotificationDebugScreen> {
  final _debugHelper = NotificationDebugHelper();
  String _statusReport = 'Tap "Generate Status Report" to see system status';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Debug'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTestSection(),
            const SizedBox(height: 24),
            _buildStatusSection(),
            const SizedBox(height: 24),
            _buildUtilitySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Test Notifications',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testLowBalanceNotification,
              child: const Text('Test Low Balance Alert'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _testThresholdNotification,
              child: const Text('Test Threshold Alert'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _testReminderNotification,
              child: const Text('Test Reminder Notification'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _testWelcomeNotification,
              child: const Text('Test Welcome Notification'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _runFullAlertCheck,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.secondary,
              ),
              child: const Text('Run Full Alert Check'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'System Status',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _generateStatusReport,
              child: const Text('Generate Status Report'),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Text(
                _statusReport,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUtilitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Utilities',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _requestPermissions,
              child: const Text('Request Permissions'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _clearAllData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Theme.of(context).colorScheme.onError,
              ),
              child: const Text('Clear All Notification Data'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testLowBalanceNotification() async {
    await _testNotification(
      'Low Balance Alert',
      'Your meter will reach zero in approximately 12 hours.',
      NotificationType.lowBalance,
    );
  }

  Future<void> _testThresholdNotification() async {
    await _testNotification(
      'Time to Top-Up',
      'You should top up in approximately 6 hours.',
      NotificationType.timeToTopUp,
    );
  }

  Future<void> _testReminderNotification() async {
    await _testNotification(
      'Meter Reading Reminder',
      'It\'s time to take a new meter reading.',
      NotificationType.readingReminder,
    );
  }

  Future<void> _testWelcomeNotification() async {
    await _testNotification(
      'Welcome to Lekky',
      'Your electricity meter tracking app is ready to use!',
      NotificationType.welcome,
    );
  }

  Future<void> _testNotification(
      String title, String message, NotificationType type) async {
    setState(() => _isLoading = true);

    try {
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();

      final notification = AppNotification(
        title: title,
        message: message,
        timestamp: DateTime.now(),
        type: type,
      );

      await notificationService.showNotification(notification);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Test notification sent: $title'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send notification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _runFullAlertCheck() async {
    setState(() => _isLoading = true);

    try {
      final alertManager = UnifiedAlertManager();
      await alertManager.checkAndFireAlerts();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Full alert check completed'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Alert check failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateStatusReport() async {
    setState(() => _isLoading = true);

    try {
      final report = await _debugHelper.generateStatusReport();

      // Format the report for display
      final buffer = StringBuffer();
      buffer.writeln('=== NOTIFICATION SYSTEM STATUS ===');
      buffer.writeln('Generated: ${DateTime.now()}');
      buffer.writeln('Status: ${report['status']}');
      buffer.writeln();

      if (report['permissions'] != null) {
        final permissions = report['permissions'] as Map<String, dynamic>;
        buffer.writeln('PERMISSIONS:');
        buffer.writeln('  Has Permission: ${permissions['has_permission']}');
        buffer.writeln();
      }

      if (report['settings'] != null) {
        final settings = report['settings'] as Map<String, dynamic>;
        buffer.writeln('SETTINGS:');
        buffer.writeln('  Notifications: ${settings['notifications_enabled']}');
        buffer.writeln('  Low Balance: ${settings['low_balance_enabled']}');
        buffer.writeln('  Threshold: ${settings['time_to_top_up_enabled']}');
        buffer.writeln('  Reminders: ${settings['reminders_enabled']}');
        buffer.writeln('  Alert Threshold: ${settings['alert_threshold']}');
        buffer.writeln('  Days in Advance: ${settings['days_in_advance']}');

        buffer.writeln();
      }

      if (report['services'] != null) {
        final services = report['services'] as Map<String, dynamic>;
        buffer.writeln('SERVICES:');
        buffer.writeln(
            '  Notification Service: ${services['notification_service_available']}');
        buffer
            .writeln('  Alert Manager: ${services['alert_manager_available']}');
        buffer.writeln();
      }

      if (report['recent_notifications'] != null) {
        final notifications =
            report['recent_notifications'] as Map<String, dynamic>;
        buffer.writeln('NOTIFICATIONS:');
        buffer.writeln(
            '  Pending System: ${notifications['pending_system_notifications'] ?? 'Unknown'}');

        final pendingInApp =
            notifications['pending_in_app_alert'] as Map<String, dynamic>?;
        if (pendingInApp?['title'] != null) {
          buffer.writeln('  Pending In-App: ${pendingInApp!['title']}');
        }
        buffer.writeln();
      }

      if (report['error'] != null) {
        buffer.writeln('ERROR: ${report['error']}');
      }

      setState(() {
        _statusReport = buffer.toString();
      });
    } catch (e) {
      setState(() {
        _statusReport = 'Error generating report: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _requestPermissions() async {
    setState(() => _isLoading = true);

    try {
      final permissionManager = NotificationPermissionManager();
      final granted = await permissionManager.requestPermission(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(granted ? 'Permissions granted' : 'Permissions denied'),
            backgroundColor: granted ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Permission request failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _clearAllData() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
            'This will clear all notification settings, scheduled notifications, '
            'and reset the notification system. Are you sure?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      await _debugHelper.clearAllNotificationData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All notification data cleared'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Refresh status report
      await _generateStatusReport();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to clear data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
